import asyncio
import websockets

# 连接到 /chat 路由的 WebSocket 服务端
async def hello_chat():
    uri = "ws://localhost:8765/echo"  # 连接到 chat 路由  /echo 连接到echo 路由
    async with websockets.connect(uri) as websocket:
        message = "Hello, this is a chat message!"
        await websocket.send(message)
        print(f"Sent: {message}")

        response = await websocket.recv()
        print(f"Received: {response}")

# 启动客户端
if __name__ == "__main__":
    asyncio.run(hello_chat())

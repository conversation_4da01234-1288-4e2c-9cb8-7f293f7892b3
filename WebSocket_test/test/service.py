import asyncio
import websockets

# 路由处理函数
async def route_handler(websocket, path):
    if path == "/chat":
        await chat_handler(websocket)
    elif path == "/echo":
        await echo_handler(websocket)
    else:
        await websocket.send(f"Unknown path: {path}")
        await websocket.close()

# Chat 路由的处理
async def chat_handler(websocket):
    print("Chat session started")
    try:
        async for message in websocket:
            print(f"Chat message: {message}")
            await websocket.send(f"Chat reply: {message}")
    except websockets.exceptions.ConnectionClosed as e:
        print(f"Chat connection closed: {e}")

# Echo 路由的处理
async def echo_handler(websocket):
    print("Echo session started")
    try:
        async for message in websocket:
            print(f"Echo message: {message}")
            await websocket.send(f"Echo: {message}")
    except websockets.exceptions.ConnectionClosed as e:
        print(f"Echo connection closed: {e}")

# 启动 WebSocket 服务，接受不同路径的连接
async def main():
    server = await websockets.serve(route_handler, "0.0.0.0", 8765)  # 将 localhost 改为 0.0.0.0
    print("WebSocket server started on ws://0.0.0.0:8765")
    await server.wait_closed()

# 运行 WebSocket 服务
if __name__ == "__main__":
    asyncio.run(main())

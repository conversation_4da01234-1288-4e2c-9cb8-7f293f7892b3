from pydub import AudioSegment
import numpy as np
import opuslib

def decode_audio(file_path):
    """
    解码本地WAV文件，获取PCM数据
    """
    try:
        # 使用 pydub 打开音频文件
        audio = AudioSegment.from_file(file_path)
    except Exception as e:
        raise ValueError(f"Error loading audio file: {e}")

    # 获取原始音频的声道和采样率
    channels = audio.channels
    frame_rate = audio.frame_rate

    print(f"Original audio channels: {channels}, sample rate: {frame_rate}")

    # 转换为单声道和 16kHz 采样率（Opus 编码要求）
    audio = audio.set_channels(1).set_frame_rate(16000)

    # 获取 PCM 数据
    pcm_data = audio.raw_data  # 这是字节数据

    # 将字节数据转换为 numpy 数组，dtype 设置为 int16（16-bit PCM）
    audio_data = np.frombuffer(pcm_data, dtype=np.int16)

    return audio_data

def encode_opus_audio(audio_data):
    """
    对PCM音频数据进行Opus编码
    """
    # 确保音频数据是 numpy.int16 数组
    if not isinstance(audio_data, np.ndarray):
        raise ValueError("Expected numpy.ndarray for audio data")

    print(f"Audio data length: {len(audio_data)} samples")

    # 创建 Opus 编码器，采样率16000Hz，单声道，应用场景为 VoIP（语音通信）
    try:
        encoder = opuslib.Encoder(16000, 1, application='voip')
    except Exception as e:
        raise ValueError(f"Error creating Opus encoder: {e}")

    # 编码音频数据，返回 Opus 编码后的数据
    # 设置帧大小为20ms（320个样本，对于16kHz采样率）
    frame_size = 320
    num_frames = len(audio_data) // frame_size
    opus_frames = []
    for i in range(num_frames):
        frame_data = audio_data[i*frame_size:(i+1)*frame_size].tobytes()
        opus_frame = encoder.encode(frame_data, frame_size)  # 注意这里传递的是样本数，不是字节数
        opus_frames.append(opus_frame)

    # 处理最后一帧（如果它存在且不足frame_size）
    if len(audio_data) % frame_size != 0:
        remaining_samples = audio_data[num_frames*frame_size:].tobytes()
        # Opus编码器可能需要一个完整的帧，或者我们可以填充数据以形成一个完整的帧
        # 这里我们简单地忽略最后一帧（不推荐用于生产环境）
        # opus_frame = encoder.encode(remaining_samples + b'\x00' * (frame_size - len(remaining_samples)//2), frame_size)
        # opus_frames.append(opus_frame)
        # 注意：上面的填充方法可能不正确，因为'\x00'填充可能不会形成有效的PCM数据
        # 在实际应用中，您可能需要更复杂的逻辑来处理这种情况

    # 将所有Opus帧连接成一个字节串
    opus_data = b''.join(opus_frames)
    return opus_data

def save_opus_file(opus_data, output_file):
    """
    保存编码后的Opus音频数据到文件
    """
    with open(output_file, 'wb') as f:
        f.write(opus_data)
    print(f"Opus encoded audio saved to: {output_file}")

# 主程序流程
if __name__ == "__main__":
    # 输入文件路径和输出文件路径
    input_wav_file = 'instruct_0.wav'  # 你可以修改为你的WAV文件路径
    output_opus_file = 'output_audio.opus'

    try:
        # 步骤1：解码音频文件为 PCM 数据
        audio_data = decode_audio(input_wav_file)

        # 步骤2：将 PCM 数据进行 Opus 编码
        opus_data = encode_opus_audio(audio_data)

        # 步骤3：保存编码后的 Opus 音频数据到文件
        save_opus_file(opus_data, output_opus_file)
    except Exception as e:
        print(f"An error occurred: {e}")
import asyncio
import websockets
import json
import struct
import numpy as np
from pydub import AudioSegment
import opuslib
import io


# 将压缩编码的音频文件转换为 PCM 格式（假设是 WAV 格式，但内部是压缩编码）
def decode_audio(file_path):
    # 使用 pydub 打开音频文件
    audio = AudioSegment.from_file(file_path)

    # 获取原始音频的声道和采样率
    channels = audio.channels
    frame_rate = audio.frame_rate

    print(f"Original audio channels: {channels}, sample rate: {frame_rate}")

    # 转换为 16-bit PCM 格式
    audio = audio.set_channels(1)  # 单声道
    audio = audio.set_frame_rate(16000)  # 设置为 16kHz 采样率

    # 获取 PCM 数据
    pcm_data = audio.raw_data  # 这是字节数据

    # 将字节数据转换为 numpy 数组，dtype 设置为 int16（16-bit PCM）
    audio_data = np.frombuffer(pcm_data, dtype=np.int16)

    # 确保返回的数据是 numpy.int16 数组
    if not isinstance(audio_data, np.ndarray):
        raise ValueError("Decoded audio data is not of type numpy.ndarray")

    # 打印一些信息帮助调试
    print(f"Audio data shape: {audio_data.shape}")
    print(f"Audio data type: {audio_data.dtype}")

    return audio_data


# 对音频数据进行 Opus 编码
def encode_opus_audio(audio_data):
    # 确保音频数据是 numpy.int16 数组
    if not isinstance(audio_data, np.ndarray):
        raise ValueError("Expected numpy.ndarray for audio data")

    # 将音频数据转换为 int16 类型（如果它不是 int16 类型的话）
    # audio_data = audio_data.astype(np.int16)
    print(len(audio_data))

    # # 创建 Opus 编码器，采样率16000Hz，单声道，应用场景为 VoIP（语音通信）
    # encoder = opuslib.Encoder(16000, 1, application='voip')  # 采样率16000Hz，1声道
    #
    # # 编码音频数据，返回 Opus 编码后的数据
    # opus_data = encoder.encode(audio_data.tobytes(), len(audio_data))

    # 创建 Opus 编码器，采样率16000Hz，单声道，应用场景为 VoIP（语音通信）
    try:
        encoder = opuslib.Encoder(16000, 1, application='restricted_lowdelay')
    except Exception as e:
        raise ValueError(f"Error creating Opus encoder: {e}")

    # 编码音频数据，返回 Opus 编码后的数据
    # 设置帧大小为20ms（320个样本，对于16kHz采样率）
    frame_size = 320
    num_frames = len(audio_data) // frame_size
    opus_frames = []
    for i in range(num_frames):
        frame_data = audio_data[i*frame_size:(i+1)*frame_size].tobytes()
        opus_frame = encoder.encode(frame_data, frame_size)  # 注意这里传递的是样本数，不是字节数
        opus_frames.append(opus_frame)

    # 将所有 Opus 帧合并成一个字节串
    opus_data = b''.join(opus_frames)
    print('--------------', len(opus_data))
    return opus_data

# 创建 WebSocket 消息头和音频数据
def create_binary_protocol(type, payload):
    payload_size = len(payload)
    # 按照协议格式构建消息头（固定头部格式）
    header = struct.pack('!B B H', type, 0, payload_size)
    # 确保 payload 是一个字节串
    if isinstance(payload, list):
        # 这里不应该发生，因为 encode_opus_audio 现在应该返回一个字节串
        # 但如果出于某种原因它返回了一个列表，我们需要处理这种情况
        raise ValueError("Payload should be a bytes object, not a list")
    return header + payload


async def send_audio(file_path, uri):
    # 连接到 WebSocket 服务器
    async with websockets.connect(uri, extra_headers={
        "Authorization": "Bearer <access_token>",  # 替换为实际的 access_token
        "Device-Id": "your_device_mac",  # 替换为实际的设备 MAC 地址
        "Protocol-Version": "3"
    }) as websocket:
        # 发送握手 hello 消息
        hello_message = {
            "type": "hello",
            "transport": "websocket",
            "version": 3,
            "response_mode": "auto",
            "audio_params": {
                "format": "opus",
                "sample_rate": 16000,
                "channels": 1,
                "frame_duration": 60
            }
        }
        await websocket.send(json.dumps(hello_message))

        # 读取并解码音频文件
        audio_data = decode_audio(file_path)

        # 将音频数据编码为 Opus 格式
        encoded_audio = encode_opus_audio(audio_data)

        # 创建 WebSocket 消息
        binary_message = create_binary_protocol(0, encoded_audio)

        # 发送音频数据
        await websocket.send(binary_message)

        # 状态切换为 listening
        state_message = json.dumps({"type": "state", "state": "listening"})
        await websocket.send(state_message)

        # 接收服务器的响应
        while True:
            received_message = await websocket.recv()

            if isinstance(received_message, bytes):
                type, reserved, payload_size = struct.unpack('!B B H', received_message[:4])
                payload = received_message[4:]
                print(f"Received binary payload with type {type}")

            elif isinstance(received_message, str):
                json_data = json.loads(received_message)
                if json_data["type"] == "tts" and json_data["state"] == "start":
                    print("Server is generating speech...")
                    # 播放音频的逻辑
                elif json_data["type"] == "tts" and json_data["state"] == "stop":
                    print("Speech finished.")
                    break  # 退出循环


async def main():
    file_path = "./zero_shot_prompt.wav"  # 替换为你的压缩编码的音频文件路径
    uri = "ws://0.0.0.0:8765/lwq/v1"  # 修改为你的本地 WebSocket 服务地址
    await send_audio(file_path, uri)


if __name__ == "__main__":
    asyncio.run(main())

import asyncio
import websockets
import struct
import json

# WebSocket 服务器端
async def handler(websocket, path):
    try:
        # 处理握手消息
        hello_message = {
            "type": "hello",
            "transport": "websocket",
            "version": 3,
            "response_mode": "auto",  # 可以根据需求设置为 auto 或 manual
            "audio_params": {
                "format": "opus",
                "sample_rate": 16000,
                "channels": 1,
                "frame_duration": 60
            }
        }
        await websocket.send(json.dumps(hello_message))

        while True:
            # 接收二进制数据包
            message = await websocket.recv()

            if isinstance(message, bytes):
                # 解析头部字段
                type, reserved, payload_size = struct.unpack('!B B H', message[:4])
                payload = message[4:]

                # 处理音频数据
                if type == 0:  # 音频流数据
                    print(f"Received audio data with size {payload_size}")
                    # payload 数据为Opus 格式的音频数据

                    # 在这里可以进行音频数据处理，模拟解码等
                    # 解码成音频，然后语音转文字，文字再给到qwen2 进行对话，得到的对话文字再通过文字转语音模型转成语音，再发回给到客户端
                    # 返回音频数据
                    await websocket.send(message)
                elif type == 1:  # JSON 数据
                    json_data = json.loads(payload.decode())
                    print(f"Received JSON: {json_data}")

                    if json_data['type'] == 'state' and json_data['state'] == 'speaking':
                        # 服务器开始合成语音时，返回 tts.start
                        tts_start_msg = json.dumps({
                            "type": "tts",
                            "state": "start"
                        })
                        await websocket.send(tts_start_msg)
                    elif json_data['type'] == 'abort':
                        # 收到打断消息，停止当前音频播放
                        tts_stop_msg = json.dumps({
                            "type": "tts",
                            "state": "stop"
                        })
                        await websocket.send(tts_stop_msg)

    except Exception as e:
        print(f"Error: {e}")

async def main():
    # 监听本地地址和端口
    server = await websockets.serve(handler, "0.0.0.0", 8765)
    print("Server running on ws://0.0.0.0:8765")
    await server.wait_closed()

if __name__ == "__main__":
    asyncio.run(main())

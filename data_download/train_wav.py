import os

wav_file = '/media/lwq/dd1/share_directory/SenseVoice/data_download/data/speech_asr_aishell_hotwords_testsets/wav/test'
wav_list = os.listdir(wav_file)

out_txt_file = 'train_text.txt'

# 将结果写入txt文件
with open(out_txt_file, 'w', encoding='utf-8') as f:
    for wav_name in wav_list:
        wav_file_name = os.path.join(wav_file, wav_name)

        wav_name_id = wav_name.split('.wav')[0]

        combined_data = wav_name_id + ' ' + wav_file_name

        f.write(combined_data + '\n')

print(f"文件已成功生成并保存为 {out_txt_file}")
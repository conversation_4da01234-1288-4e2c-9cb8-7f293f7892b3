import pandas as pd

# 读取CSV文件
input_file = './data/speech_asr_aishell_hotwords_testsets.csv'  # 替换为您的输入文件路径
output_file = 'train_wav.txt'  # 输出文件路径

# 读取CSV文件
df = pd.read_csv(input_file)

# 提取文件名和文本标签
file_names = df['Audio:FILE'].str.split('/').str[-1].str.split('.').str[0]
text_labels = df['Text:LABEL'].str.replace(' ', '')  # 去除文本标签中的空格

print(text_labels)
# 将文件名和文本标签组合成指定格式
combined_data = file_names + ' ' +text_labels

# 将结果写入txt文件
with open(output_file, 'w', encoding='utf-8') as f:
    for item in combined_data:
        f.write(item + '\n')

print(f"文件已成功生成并保存为 {output_file}")

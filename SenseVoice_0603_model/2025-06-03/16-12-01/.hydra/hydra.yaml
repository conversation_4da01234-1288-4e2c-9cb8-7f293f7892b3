hydra:
  run:
    dir: outputs/${now:%Y-%m-%d}/${now:%H-%M-%S}
  sweep:
    dir: multirun/${now:%Y-%m-%d}/${now:%H-%M-%S}
    subdir: ${hydra.job.num}
  launcher:
    _target_: hydra._internal.core_plugins.basic_launcher.BasicLauncher
  sweeper:
    _target_: hydra._internal.core_plugins.basic_sweeper.BasicSweeper
    max_batch_size: null
    params: null
  help:
    app_name: ${hydra.job.name}
    header: '${hydra.help.app_name} is powered by Hydra.

      '
    footer: 'Powered by Hydra (https://hydra.cc)

      Use --hydra-help to view Hydra specific help

      '
    template: '${hydra.help.header}

      == Configuration groups ==

      Compose your configuration from those groups (group=option)


      $APP_CONFIG_GROUPS


      == Config ==

      Override anything in the config (foo.bar=value)


      $CONFIG


      ${hydra.help.footer}

      '
  hydra_help:
    template: 'Hydra (${hydra.runtime.version})

      See https://hydra.cc for more info.


      == Flags ==

      $FLAGS_HELP


      == Configuration groups ==

      Compose your configuration from those groups (For example, append hydra/job_logging=disabled
      to command line)


      $HYDRA_CONFIG_GROUPS


      Use ''--cfg hydra'' to Show the Hydra config.

      '
    hydra_help: ???
  hydra_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][HYDRA] %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
    root:
      level: INFO
      handlers:
      - console
    loggers:
      logging_example:
        level: DEBUG
    disable_existing_loggers: false
  job_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][%(name)s][%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
      file:
        class: logging.FileHandler
        formatter: simple
        filename: ${hydra.runtime.output_dir}/${hydra.job.name}.log
    root:
      level: INFO
      handlers:
      - console
      - file
    disable_existing_loggers: false
  env: {}
  mode: RUN
  searchpath: []
  callbacks: {}
  output_subdir: .hydra
  overrides:
    hydra:
    - hydra.mode=RUN
    task:
    - ++model=./SenseVoice_model
    - ++trust_remote_code=true
    - ++train_data_set_list=/media/lwq/code/SenseVoice/data/train_example.jsonl
    - ++valid_data_set_list=/media/lwq/code/SenseVoice/data/val_example.jsonl
    - ++dataset_conf.data_split_num=1
    - ++dataset_conf.batch_sampler=BatchSampler
    - ++dataset_conf.batch_size=6000
    - ++dataset_conf.sort_size=1024
    - ++dataset_conf.batch_type=token
    - ++dataset_conf.num_workers=4
    - ++train_conf.max_epoch=50
    - ++train_conf.log_interval=1
    - ++train_conf.resume=true
    - ++train_conf.validate_interval=2000
    - ++train_conf.save_checkpoint_interval=2000
    - ++train_conf.keep_nbest_models=20
    - ++train_conf.avg_nbest_model=10
    - ++train_conf.use_deepspeed=false
    - ++train_conf.deepspeed_config=/media/lwq/code/SenseVoice/deepspeed_conf/ds_stage1.json
    - ++optim_conf.lr=0.0002
    - ++output_dir=./outputs
  job:
    name: train_ds
    chdir: null
    override_dirname: ++dataset_conf.batch_sampler=BatchSampler,++dataset_conf.batch_size=6000,++dataset_conf.batch_type=token,++dataset_conf.data_split_num=1,++dataset_conf.num_workers=4,++dataset_conf.sort_size=1024,++model=./SenseVoice_model,++optim_conf.lr=0.0002,++output_dir=./outputs,++train_conf.avg_nbest_model=10,++train_conf.deepspeed_config=/media/lwq/code/SenseVoice/deepspeed_conf/ds_stage1.json,++train_conf.keep_nbest_models=20,++train_conf.log_interval=1,++train_conf.max_epoch=50,++train_conf.resume=true,++train_conf.save_checkpoint_interval=2000,++train_conf.use_deepspeed=false,++train_conf.validate_interval=2000,++train_data_set_list=/media/lwq/code/SenseVoice/data/train_example.jsonl,++trust_remote_code=true,++valid_data_set_list=/media/lwq/code/SenseVoice/data/val_example.jsonl
    id: ???
    num: ???
    config_name: null
    env_set: {}
    env_copy: []
    config:
      override_dirname:
        kv_sep: '='
        item_sep: ','
        exclude_keys: []
  runtime:
    version: 1.3.2
    version_base: '1.3'
    cwd: /media/lwq/code/SenseVoice
    config_sources:
    - path: hydra.conf
      schema: pkg
      provider: hydra
    - path: ''
      schema: structured
      provider: schema
    output_dir: /media/lwq/code/SenseVoice/outputs/2025-06-03/16-12-01
    choices:
      hydra/env: default
      hydra/callbacks: null
      hydra/job_logging: default
      hydra/hydra_logging: default
      hydra/hydra_help: default
      hydra/help: default
      hydra/sweeper: basic
      hydra/launcher: basic
      hydra/output: default
  verbose: false

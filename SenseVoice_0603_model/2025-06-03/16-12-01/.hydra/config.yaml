model: ./SenseVoice_model
trust_remote_code: true
train_data_set_list: /media/lwq/code/SenseVoice/data/train_example.jsonl
valid_data_set_list: /media/lwq/code/SenseVoice/data/val_example.jsonl
dataset_conf:
  data_split_num: 1
  batch_sampler: BatchSampler
  batch_size: 6000
  sort_size: 1024
  batch_type: token
  num_workers: 4
train_conf:
  max_epoch: 50
  log_interval: 1
  resume: true
  validate_interval: 2000
  save_checkpoint_interval: 2000
  keep_nbest_models: 20
  avg_nbest_model: 10
  use_deepspeed: false
  deepspeed_config: /media/lwq/code/SenseVoice/deepspeed_conf/ds_stage1.json
optim_conf:
  lr: 0.0002
output_dir: ./outputs

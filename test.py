from funasr import AutoModel
from funasr.utils.postprocess_utils import rich_transcription_postprocess

model_dir = "./SenseVoice_model"


model = AutoModel(
    model=model_dir,
    trust_remote_code=True,
    remote_code="./model.py",
    vad_model="fsmn-vad",
    vad_kwargs={"max_single_segment_time": 30000},
    device="cuda:0",
)

# en
res = model.generate(
    input="./voice_test/语音识别.mp3",  #
    cache={},
    language="auto",  # "zh", "en", "yue", "ja", "ko", "nospeech"
    use_itn=True,
    batch_size_s=60,
    merge_vad=True,
    merge_length_s=15,
)

print('---------res---------:{}'.format(res))
text = rich_transcription_postprocess(res[0]["text"])
print(text)
# 南网慧时项目表计算法数据标注，钢筋测距算法测试数据标注
# 南网会时项目标计算法数据标注钢精测计算法测试数据标注。
# 南网会时项目标计算法数据标注钢精测计算法测试数据标注。

# 微调后-南网汇时项目表计算法数据标注钢筋测距算法测试数据标注